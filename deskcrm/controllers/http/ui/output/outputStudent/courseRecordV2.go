package outputStudent

type CourseRecordV2Resp struct {
	SeasonMetaData        []SeasonMetaData        `json:"seasonMetaData"`
	NewCourseTypeMetaData []NewCourseTypeMetaData `json:"newCourseTypeMetaData"`
	// Deprecated:此字段无数据
	ServiceTypeMetaData []ServiceTypeMetaData `json:"serviceTypeMetaData"`
	RefundMetaData      []RefundMetaData      `json:"refundMetaData"`
	CourseList          []CourseInfo          `json:"courseList"`
	CourseTypeCut       []int                 `json:"courseTypeCut"`
}

// 季节元数据
type SeasonMetaData struct {
	Label  string `json:"label"`
	Count  int    `json:"count"`
	Season int    `json:"season"`
}

// 课程类型元数据
type NewCourseTypeMetaData struct {
	Label         string `json:"label"`
	Count         int    `json:"count"`
	NewCourseType int    `json:"newCourseType"`
}

// 服务类型元数据
type ServiceTypeMetaData struct {
	// 根据提供的JSON，这个结构体目前是空的
}

// 退款元数据
type RefundMetaData struct {
	Label string `json:"label"`
	Count int    `json:"count"`
}

// 选项卡信息
type TabInfo struct {
	Type     string `json:"type"`
	Label    string `json:"label"`
	IsExport int    `json:"isExport"`
}

type CourseRecordTag struct {
	Label string `json:"label"`
	Color string `json:"color"`
}

// 课程信息
type CourseInfo struct {
	CourseId          int64             `json:"courseId"`
	CourseName        string            `json:"courseName"`
	Pay               string            `json:"pay"`
	TradeTime         int64             `json:"tradeTime"`
	TradeStatus       int               `json:"tradeStatus"`
	OnlineFormatTime  string            `json:"onlineFormatTime"`
	StartTime         int64             `json:"startTime"`
	Grade             int               `json:"grade"`
	Subject           int               `json:"subject"`
	SubjectName       string            `json:"subjectName"`
	DeptId            int               `json:"deptId"`
	ExamType          int               `json:"examType"`
	TeacherName       string            `json:"teacherName"`
	AssistantUid      int64             `json:"assistantUid"`
	AssistantName     string            `json:"assistantName"`
	StaffName         string            `json:"staffName"`
	DeviceName        string            `json:"deviceName"`
	CourseReviewUrl   string            `json:"courseReviewUrl"`
	NewCourseType     int               `json:"newCourseType"`
	CourseServiceType string            `json:"courseServiceType"`
	GradeStage        int               `json:"gradeStage"`
	RefundTime        int64             `json:"refundTime"`
	Year              int               `json:"year"`
	Season            int               `json:"season"`
	Source            int               `json:"source"`
	CoursePriceTag    int               `json:"coursePriceTag"`
	HasStudyPlan      int               `json:"hasStudyPlan"`
	StudyPlanScore    int               `json:"studyPlanScore"`
	GjkCompleteRate   string            `json:"gjkCompleteRate"`
	CourseServiceTag  string            `json:"courseServiceTag"`
	CourseStatusTag   string            `json:"courseStatusTag"`
	TagList           []CourseRecordTag `json:"tagList"`
	Shoulder          []string          `json:"shoulder"`
	SchemaId          int               `json:"schemaId"`
	TabList           []TabInfo         `json:"tabList,omitempty"`
}
