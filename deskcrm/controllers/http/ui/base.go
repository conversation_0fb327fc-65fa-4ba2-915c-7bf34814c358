package ui

import (
	"deskcrm/middleware"

	"github.com/gin-gonic/gin"
)

// RegisterHandlers crm ui 接口调用入口
func RegisterHandlers(rg *gin.RouterGroup) {
	ui := rg.Group("/ui/")
	// ------------------------------------------------ui router-------------------------------------------------------
	ui.Use(middleware.AuthCheck())
	crmRouter := ui.Group("/", middleware.SelectBusiness)
	crmNotCheckSelectBusinessRouter := ui.Group("/")

	courseGroup := crmRouter.Group("/course/")
	{
		courseGroup.POST("courselistandcardbyyear", CourseController.CourseListAndCardByYear)
		courseGroup.GET("courselistandcardbyyear", CourseController.CourseListAndCardByYear)

		courseGroup.POST("getLessonplayinfolist", CourseController.GetLessonPlayInfoList)
		courseGroup.GET("getLessonplayinfolist", CourseController.GetLessonPlayInfoList)
		courseGroup.Any("getsipinfo", CourseController.GetSipInfo)
	}

	sopGroup := crmRouter.Group("/sop/")
	{
		sopGroup.Any("getsipinfo", CourseController.GetSipInfo)
		sopGroup.Any("getcontactflagoption", StudentController.GetContactFlagOption)
		sopGroup.Any("getstudentcallrecordinfo", StudentController.GetStudentCallRecordInfo)
		sopGroup.Any("getcalltypelist", StudentController.GetCallTypeList)
	}

	tagGroup := crmRouter.Group("/tag/")
	{
		tagGroup.Any("/getstudentbind", StudentController.GetStudentBind)
		tagGroup.Any("/getactivewithbinddata", StudentController.GetActiveWithBindData)
	}

	templateGroup := crmRouter.Group("/template/")
	{
		templateGroup.Any("/getactivewithbinddata", StudentController.GetActiveWithBindData)
	}

	customFieldGroup := crmRouter.Group("/customfield/")
	{
		customFieldGroup.Any("/getoptions", StudentController.GetCustomFieldOptions)
	}

	// 用户相关路由
	userGroup := crmRouter.Group("/user/")
	{
		userGroup.Any("userinfo", UserController.GetUserInfo)
		userGroup.Any("commongray", StudentController.CommonGray)
	}

	// 维系详情相关路由
	studentGroup := crmRouter.Group("/student/")
	{
		studentGroup.Any("getcoursetimetableday", StudentController.GetCourseTimeTableDay)
		studentGroup.Any("getcoursetimetableweek", StudentController.GetCourseTimeTableWeek)
		studentGroup.Any("getstudentorderlist", StudentController.GetStudentOrderList)
		studentGroup.Any("interviewreferlpc", StudentController.InterviewReferLpc)
		studentGroup.Any("interviewrecord", StudentController.GetInterviewRecord)
		studentGroup.Any("interviewrecordv2", StudentController.GetInterviewRecordV2)
		studentGroup.Any("detailconfig", StudentDetailConfigController.GetDetailConfig)
		studentGroup.Any("getwxbindinfo", StudentController.GetWxBindInfo)
		studentGroup.Any("keybehavior", StudentController.GetKeyBehavior)
		studentGroup.Any("/studentcallinfo", StudentController.GetStudentCallInfo)
		studentGroup.Any("/studentdelaminationdiffrencelistv1", StudentController.StudentDelaminationDifferenceListV1)
		studentGroup.Any("getstudentdetailpageoption", StudentController.GetDetailPageOption)
		studentGroup.Any("courserecorddefaultoption", StudentController.GetCourseRecordDefaultOption)
		studentGroup.Any("courserecordmeta", StudentController.GetCourseRecordMeta)
		studentGroup.Any("performancev1", StudentController.GetPerformanceV1)
		studentGroup.Any("studentdetailv1", StudentController.StudentDetailV1)
		studentGroup.Any("courserecordv2", StudentController.CourseRecordV2)
		studentGroup.Any("getstudentrelationlist", StudentController.GetStudentRelationList)
		studentGroup.Any("/setbelonger", StudentController.SetBelonger)
		studentGroup.Any("/getbelongermap", StudentController.GetBelongerMap)
	}

	// 维系详情配置
	keepDetailConfigGroup := crmRouter.Group("/detailconfig/")
	{
		keepDetailConfigGroup.Any("getschemabycourseid", KeepDetailConfigController.GetSchemaByCourseId)
	}

	taskGroup := crmRouter.Group("/task/")
	taskForPredataGroup := crmNotCheckSelectBusinessRouter.Group("/task/")
	{
		taskGroup.POST("collection", ArkController.Collection)
		taskGroup.GET("collection", ArkController.Collection)

		taskGroup.POST("predatacollection", ArkController.PreDataCollection)
		taskGroup.GET("predatacollection", ArkController.PreDataCollection)

		taskGroup.GET("getpagingconfig", MercuryController.GetPagingConfig)

		taskGroup.GET("lessoninfo", LessonController.GetLessonList)
		taskForPredataGroup.GET("predatalessoninfo", LessonController.GetLessonList)
	}

	filterGroup := crmRouter.Group("/filter/")
	{
		filterGroup.POST("getfilterconfig", ArkController.GetFilterConfig)
		filterGroup.GET("getfilterconfig", ArkController.GetFilterConfig)

		filterGroup.POST("studentlist", StudentController.GetStudentList)
	}

	arkGroup := crmRouter.Group("/ark/")
	{
		arkGroup.GET("isshownewphonecell", PhoneController.IsShowNewPhoneCall)

		arkGroup.GET("getfieldmaptree", ArkController.GetFieldMapTree)

		arkGroup.POST("gettemplate", ArkController.GetTemplate)

		arkGroup.POST("addrule", ArkController.AddArkRule)
		arkGroup.POST("delrule", ArkController.DelArkRule)
		arkGroup.POST("ruledetail", ArkController.RuleDetail)
		arkGroup.POST("rulelist", ArkController.RuleList)

	}

	// 自定义标签
	customTagGroup := crmRouter.Group("/customtag/")
	{
		customTagGroup.Any("batchaddtag", CustomTagController.BatchAddTag)
		customTagGroup.Any("batchdeltag", CustomTagController.BatchDelTag)
		customTagGroup.Any("edittag", CustomTagController.EditTag)
		customTagGroup.Any("getcustomtag", CustomTagController.GetCustomTag)
		customTagGroup.Any("getcustomtaglist", CustomTagController.GetCustomTagList)
	}
}
