package util

import (
	"fmt"
	"gorm.io/gorm/utils"
	"strconv"
)

func InArrayString(e string, array []string) bool {
	for _, element := range array {
		if e == element {
			return true
		}
	}

	return false
}

func ChunkArrayInt(array []int, size int) [][]int {
	if len(array) == 0 || size <= 0 {
		return nil
	}

	total := len(array) / size
	if len(array)%size != 0 {
		total++
	}

	result := make([][]int, 0, total)
	for i := 0; i < len(array); {
		stop := i + size
		if stop > len(array) {
			stop = len(array)
		}
		item := array[i:stop]
		result = append(result, item)

		i = stop
	}

	return result
}

func ChunkArrayInt64(array []int64, size int) [][]int64 {
	if len(array) == 0 || size <= 0 {
		return nil
	}

	total := len(array) / size
	if len(array)%size != 0 {
		total++
	}

	result := make([][]int64, 0, total)
	for i := 0; i < len(array); {
		stop := i + size
		if stop > len(array) {
			stop = len(array)
		}
		item := array[i:stop]
		result = append(result, item)

		i = stop
	}

	return result
}

func ConvertArrayIntToArrayString(input []int64) []string {
	ret := make([]string, 0, len(input))
	for _, e := range input {
		ret = append(ret, utils.ToString(e))
	}

	return ret
}

func ConvertArrayStringToArrayInt(input []string) ([]int, error) {
	ret := make([]int, 0, len(input))
	for _, e := range input {
		intVal, err := strconv.Atoi(e)
		if err != nil {
			return nil, err
		}

		ret = append(ret, intVal)
	}

	return ret, nil
}

func ConvertArrayFloat64ToArrayInt(input []float64) []int {
	ret := make([]int, 0, len(input))
	for _, e := range input {
		ret = append(ret, int(e))
	}

	return ret
}

func GetMinNum(ary []int) int {
	if len(ary) == 0 {
		return 0
	}

	minVal := ary[0]
	for i := 1; i < len(ary); i++ {
		if minVal > ary[i] {
			minVal = ary[i]
		}
	}

	return minVal
}

func UniqueInt(ids []int) []int {
	m := make(map[int]struct{})
	for _, id := range ids {
		m[id] = struct{}{}
	}

	result := make([]int, 0, len(m))
	for id := range m {
		result = append(result, id)
	}

	return result
}

func UniqueInt64(ids []int64) []int64 {
	m := make(map[int64]struct{})
	for _, id := range ids {
		m[id] = struct{}{}
	}

	result := make([]int64, 0, len(m))
	for id := range m {
		result = append(result, id)
	}

	return result
}

// 求交集
func IntSliceIntersect(a, b []int) []int {
	if 0 == len(a) || 0 == len(b) {
		return nil
	}
	aValMap := make(map[int]bool)
	for _, v := range a {
		aValMap[v] = true
	}

	result := make([]int, 0)
	for _, v := range b {
		if aValMap[v] {
			result = append(result, v)
		}
	}

	return result
}

func RemoveElements(a []int64, b []int64) []int64 {
	bSet := make(map[int64]struct{})
	for _, v := range b {
		bSet[v] = struct{}{}
	}

	var result []int64
	for _, v := range a {
		if _, found := bSet[v]; !found {
			result = append(result, v)
		}
	}
	return result
}

// Contains 判断元素 elem 是否存在于切片 slice 中
func Contains[T comparable](slice []T, elem T) bool {
	for _, v := range slice {
		if v == elem {
			return true
		}
	}
	return false
}

func ToSlice[T any](v interface{}) ([]T, error) {
	if slice, ok := v.([]T); ok {
		return slice, nil
	}
	return nil, fmt.Errorf("无法将类型 %T 转换为 []%T", v, *new(T))
}

func ArrayDiff[T comparable](a, b []T) []T {
	// 创建一个map来存储数组b中的所有元素
	bMap := make(map[T]struct{})
	for _, val := range b {
		bMap[val] = struct{}{}
	}

	// 创建结果数组
	var diff []T

	// 遍历数组a，检查元素是否存在于bMap中
	for _, val := range a {
		// 如果元素不在bMap中，则添加到结果数组
		if _, exists := bMap[val]; !exists {
			diff = append(diff, val)
		}
	}

	return diff
}

func RemoveDuplicates(nums []int) []int {
	seen := make(map[int]struct{})
	result := make([]int, 0, len(nums))

	for _, num := range nums {
		if _, ok := seen[num]; !ok {
			seen[num] = struct{}{}
			result = append(result, num)
		}
	}

	return result
}
