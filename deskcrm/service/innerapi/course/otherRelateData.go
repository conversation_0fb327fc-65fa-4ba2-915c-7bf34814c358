package course

import (
	"deskcrm/api/moat"
	"fmt"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/mitchellh/mapstructure"
	"github.com/spf13/cast"
	"strings"
	"time"
)

// GetSkuIdByCourseIds 根据课程ids获取skuId
func GetSkuIdByCourseIds(ctx *gin.Context, courseIds []int64) (skuIdMap map[int64]int64, err error) {
	skuIdMap = make(map[int64]int64)

	skus, err := moat.NewClient().GetSkuListByCourseIds(ctx, courseIds)
	if err != nil {
		return nil, err
	}

	for _, item := range skus {
		skuIdMap[cast.ToInt64(item.ThirdId)] = item.SkuId
	}

	return
}

// GetSkuInfoBySkuIds 获取 Sku 信息
func GetSkuInfoBySkuIds(ctx *gin.Context, skuIds []int64) (skuInfoMap map[int64]moat.SkuInfos, err error) {
	skuInfoMap = make(map[int64]moat.SkuInfos)

	skus, err := moat.NewClient().GetSkuBaseInfoBySkuIds(ctx, skuIds)
	if err != nil {
		return nil, err
	}

	for _, item := range skus {
		skuInfoMap[item.ThirdId] = item
	}

	return
}

// FormatSkuInfo 获取 Sku 信息
func FormatSkuInfo(ctx *gin.Context, skuInfoMap map[int64]moat.SkuInfos, subjectNameMap map[int64]string, seasonName map[int]string) (formatSkuInfoMap map[int64]moat.FormatSkuInfos, err error) {
	formatSkuInfoMap = make(map[int64]moat.FormatSkuInfos)

	for courseId, baseSkuInfo := range skuInfoMap {
		data, ok := formatSkuInfoMap[courseId]
		if !ok {
			data = moat.FormatSkuInfos{}
		}
		data.CourseTimeFormat = courseTimeFormat(&baseSkuInfo)
		data.LessonDisplayTxt = lessonDisplayTxt(&baseSkuInfo)
		data.LessonNum = lessonNum(&baseSkuInfo)
		data.TeacherNameList = teacherNameList(ctx, &baseSkuInfo)
		data.LearnSeason = learnSeason(ctx, &baseSkuInfo, seasonName)
		data.Subject = subject(ctx, subjectNameMap, &baseSkuInfo)
		data.OnlineFormatTimeAll = onlineFormatTimeAll(ctx, &data)
		data.OnlineFormatTime = onlineFormatTime(ctx, &data)

		formatSkuInfoMap[courseId] = data
	}
	return
}

// 课程概览
func onlineFormatTimeAll(ctx *gin.Context, info *moat.FormatSkuInfos) (dataFormat string) {
	return fmt.Sprintf("%s %s %v %v %s %s", info.CourseTimeFormat, info.LessonDisplayTxt, info.LessonNum, strings.Join(info.TeacherNameList, " "), info.LearnSeason, info.Subject)
}

// 课程概览
func onlineFormatTime(ctx *gin.Context, info *moat.FormatSkuInfos) (dataFormat string) {
	return fmt.Sprintf("%s %s %v %s %s", info.CourseTimeFormat, info.LessonDisplayTxt, info.LessonNum, info.LearnSeason, info.Subject)
	return
}

// 学科
func subject(ctx *gin.Context, subjectNameMap map[int64]string, baseSkuInfo *moat.SkuInfos) (dataFormat string) {
	if data, ok := baseSkuInfo.AttributeTags["subject"]; ok {
		var temp []interface{}
		err := mapstructure.Decode(data, &temp)
		if err != nil {
			zlog.Warnf(ctx, "learnSeason err,%v,%v", err, data)
			return
		}
		if len(temp) > 0 {
			return subjectNameMap[cast.ToInt64(temp[0])]
		}
	}

	return
}

// 期次
func learnSeason(ctx *gin.Context, baseSkuInfo *moat.SkuInfos, seasonName map[int]string) (dataFormat string) {
	if data, ok := baseSkuInfo.LabelTags["semester"]; ok {
		var temp []interface{}
		err := mapstructure.Decode(data, &temp)
		if err != nil {
			zlog.Warnf(ctx, "learnSeason err,%v,%v", err, data)
			return
		}
		if len(temp) > 0 {
			s := seasonName[cast.ToInt(temp[0])]
			return strings.Replace(s, "_", "", -1)
		}
	}

	return
}

// 主讲
func teacherNameList(ctx *gin.Context, baseSkuInfo *moat.SkuInfos) (dataFormat []string) {
	if data, ok := baseSkuInfo.SpecTags["teacherNameList"]; ok {
		err := mapstructure.Decode(data, &dataFormat)
		if err != nil {
			zlog.Warnf(ctx, "teacherNameList err,%v,%v", err, data)
			return
		}
	}

	return
}

// 课次
func lessonNum(baseSkuInfo *moat.SkuInfos) (dataFormat string) {
	if data, ok := baseSkuInfo.LabelTags["lessonNum"]; ok {
		num := cast.ToInt64(data)
		if num != 0 {
			return fmt.Sprintf("共%d次课", num)
		}
	}

	return
}

// 上课时间
func lessonDisplayTxt(baseSkuInfo *moat.SkuInfos) (dataFormat string) {
	if data, ok := baseSkuInfo.SpecTags["clientLessonDisplayTxt"]; ok {
		str := cast.ToString(data)
		if str != "" {
			return str
		}
	}

	if data, ok := baseSkuInfo.SpecTags["lessonDisplayTxt"]; ok {
		str := cast.ToString(data)
		if str != "" {
			return str
		}
	}
	return
}

// 上课周期
func courseTimeFormat(baseSkuInfo *moat.SkuInfos) (timeFormat string) {
	var openTime, closeTime int64
	openCourseTime, ok := baseSkuInfo.SpecTags["openCourseTime"]
	if ok && openCourseTime != nil {
		openTime = cast.ToInt64(openCourseTime)
	}

	closeCourseTime, ok := baseSkuInfo.SpecTags["closeCourseTime"]
	if ok && closeCourseTime != nil {
		closeTime = cast.ToInt64(closeCourseTime)
	}

	if openTime != 0 && closeTime != 0 {
		openStr := time.Unix(openTime, 0).Format("2006-01-02")
		closeStr := time.Unix(closeTime, 0).Format("2006-01-02")

		openArr := strings.Split(openStr, "-")
		closeArr := strings.Split(closeStr, "-")

		if openArr[0] != closeArr[0] {
			timeFormat = openArr[0] + "年" + openArr[1] + "月" + openArr[2] + "日" + "-" + closeArr[0] + "年" + closeArr[1] + "月" + closeArr[2] + "日"
		} else {
			timeFormat = openArr[1] + "月" + openArr[2] + "日" + "-" + closeArr[1] + "月" + closeArr[2] + "日"
		}
	}
	return
}
