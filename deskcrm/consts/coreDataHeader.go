package consts

// CoreDataHeader 核心数据header常量定义
// 迁移自 AssistantDesk_Data_CoreDataHeader
const (
	// 核心数据header
	HEADER_LESSONNAME                              = "lessonName" // 章节名称
	HEADER_LESSONID                                = "lessonId"   // 章节ID
	HEADER_TYPE                                    = "type"
	HEADER_PLAYTYPE                                = "playType"
	HEADER_STARTTIME                               = "startTime"                          // 上课时间
	HEADER_STOPTTIME                               = "stopTime"                           // 下课时间
	HEADER_INCLASSTIME                             = "inclassTime"                        // 直播时长
	HEADER_PREVIEW                                 = "preview"                            // 预习
	HEADER_ATTEND                                  = "attend"                             // 到课
	HEADER_PLAYBACK                                = "playback"                           // 回放观看时长
	HEADER_PLAYBACK_V1                             = "playbackv1"                         // 回放观看时长新
	HEADER_LBPATTENDDURATION                       = "lbpAttendDuration"                  // Lbp观看时长
	HEADER_LBPATTENDDURATIONOLD                    = "lbpAttendDurationOld"               // Lbp观看时长
	HEADER_PLAYBACKONLINETIME                      = "playbackOnlineTime"                 // 回放在线时长
	HEADER_INCLASSTEST                             = "inclassTest"                        // 堂堂测
	HEADER_ORALQUESTION                            = "oralQuestion"                       // 口述题
	HEADER_HOMEWORK                                = "homework"                           // 巩固练习
	HEADER_HOMEWORK_LIKE                           = "similarHomework"                    // 巩固练习相似题
	HEADER_EXERCISE                                = "exercise"                           // 直播互动题
	HEADER_EXERCISEALL                             = "exerciseAll"                        // 观看互动题
	HEADER_LBPINTERACTEXAM                         = "lbpInteractExam"                    // 录播互动题
	HEADER_MIX_PLAYBACK_INTERACT                   = "mixPlaybackInteract"                // 融合直播间回放互动题
	HEADER_IS_INCLASS_TEACHER_ROOM_ATTEND_30MINUTE = "isInclassTeacherRoomAttend30minute" // "到课情况"（直播章节）
	HEADER_IS_ATTEND_FINISH                        = "isAttendFinish"                     // "完课情况"（直播章节）
	HEADER_GJK_ATTEND_LESSON_LUBO                  = "gjkAttendLessonLubo"                // "到课情况"（录播章节）
	HEADER_GJK_COMPLETE_LESSON_LUBO                = "gjkCompleteLessonLubo"              // "完课情况"（录播章节）
	HEADER_GJK_LESSON_TAG                          = "gjkLessonTag"                       // 必看章节

	HEADER_LITTLE_KID_FUDAO_HOMEWORK_STATUS = "littleKidFudaoHomeworkStatus" // 小鹿辅导作业状态
	HEADER_LITTLE_KID_FUDAO_HOMEWORK_LEVEL  = "littleKidFudaoHomeworkLevel"  // 小鹿辅导作业成绩
	HEADER_LITTLE_KID_FUDAO_INTERACT        = "littleKidFudaoInteract"       // 小鹿辅导互动题对答总

	HEADER_SYNCHRONOUSPRACTICE  = "synchronousPractice"  // 同步练习
	HEADER_HASCOMPOSITIONREPORT = "hasCompositionReport" // 作文报告

	// 课堂表现header
	HEADER_PRAISE            = "praise"            // 表扬
	HEADER_MICROPHONE        = "microphone"        // 抢麦
	HEADER_TALK              = "talk"              // 课中聊天
	HEADER_SCORE             = "score"             // 学分
	HEADER_REWARDNUM         = "rewardNum"         // 星星数
	HEADER_MONTHLYEXAMREPORT = "monthlyExamReport" // 考试报告

	// 试卷数据header
	HEADER_TESTTYPE      = "testType"      // 测试类型
	HEADER_EXAMCOUNT     = "examCount"     // 题目数量
	HEADER_PASSRULE      = "passRule"      // 合格标准
	HEADER_MAXANSWERTIME = "maxAnswerTime" // 考试时长
	HEADER_ANSWERTIME    = "answerTime"    // 答题用时
	HEADER_RIGHTCOUNT    = "rightCount"    // 正确题数
	HEADER_EXAM_SCORE    = "exam_score"    // 得分
	HEADER_REPORTURL     = "reportUrl"     // 报告

	// lpc核心数据
	HEADER_LPC_LESSONNAME                  = "lpclessonName"                // 章节名称
	HEADER_LPC_TEACHERNAME                 = "teacherName"                  // 主讲
	HEADER_LPC_STARTTIME                   = "lpcStartTime"                 // 上课时间
	HEADER_LPC_ATTENDSTATUS                = "attendStatus"                 // 到课
	HEADER_LPC_FINISHSTATUS                = "finishStatus"                 // 完课
	HEADER_LPC_PLAYSTATUS                  = "playStatus"                   // 回放
	HEADER_LPC_PREVIEW                     = "preView"                      // 预习（对/答/总）
	HEADER_LPC_TANGTANGEXAMSTAT            = "tangtangExamStat"             // 堂堂测（对/答/总）
	HEADER_LPC_STRENGTHPRACTICE            = "strengthPracticeStatus"       // 巩固练习
	HEADER_LPC_LESSONREPORTURL             = "lessonReportUrl"              // 课堂报告
	HEADER_DEER_ELOQUENCE_HOMEWORKLEVEL    = "deerEloquenceHomeworkLevel"   // 小鹿口才作业状态
	HEADER_DEER_PROGRAMMING_HOMEWORK_LEVEL = "deerProgrammingHomeworkLevel" // 小鹿编程作业状态
	HEADER_LESSON_REPORT                   = "deerLessonReportUrl"          // 小鹿ai章节报告
	HEADER_LESSON_HOMEWORK                 = "deerLessonHomeWork"           // 小鹿课程作业
	HEADER_ZHIBO_LESSON_REPORT             = "zhiboLessonReportUrl"         // 直播课程报告
	HEADER_IS_AI_ATTEND                    = "isAIAttendLesson"             // AI到课
	HEADER_IS_AI_OVERCLASS                 = "isAILessonOverclass"          // AI下课
	HEADER_AI_CONTENTTIME                  = "deerAIContentTime"            // 小鹿AI内容时长
	HEADER_DEER_LPC_BC_HOMEWORK            = "deerLpcBcHomeworkStatus"      // 小鹿LPC BC作业状态
)

// HeaderItem 表头配置项结构
type HeaderItem struct {
	Label       string   `json:"label"`                 // 显示标签
	CName       string   `json:"cname"`                 // 组件名称
	Prop        string   `json:"prop"`                  // 属性名
	Sort        int      `json:"sort"`                  // 排序
	Hover       string   `json:"hover"`                 // 悬停提示
	Width       int      `json:"width"`                 // 宽度
	Fixed       bool     `json:"fixed"`                 // 是否固定
	Tooltip     bool     `json:"tooltip"`               // 是否显示提示
	Remark      string   `json:"remark"`                // 备注
	LinkUrl     string   `json:"linkUrl,omitempty"`     // 链接URL
	FilterMap   []string `json:"filterMap,omitempty"`   // 过滤映射
	DialogWidth int      `json:"dialogWidth,omitempty"` // 对话框宽度
	CopyLinkUrl int      `json:"copyLinkUrl,omitempty"` // 复制链接URL
}

// HeaderMap 表头映射配置
// 迁移自 AssistantDesk_Data_CoreDataHeader::$headerMap
var HeaderMap = map[string]HeaderItem{
	HEADER_LESSONNAME: {
		Label:   "章节名称",
		CName:   "CommonText",
		Prop:    "lessonName",
		Sort:    0,
		Hover:   "",
		Width:   150,
		Fixed:   true,
		Tooltip: true,
		Remark:  "原辅导",
	},
	HEADER_LPC_LESSONNAME: {
		Label:   "章节名称",
		CName:   "CommonText",
		Prop:    "lessonName",
		Sort:    0,
		Hover:   "",
		Width:   150,
		Fixed:   true,
		Tooltip: true,
		Remark:  "原Lpc",
	},
	HEADER_STARTTIME: {
		Label:   "上课时间",
		CName:   "ClassTime",
		Prop:    "startTime",
		Sort:    1,
		Hover:   "",
		Width:   150,
		Fixed:   false,
		Tooltip: false,
		Remark:  "原辅导",
	},
	HEADER_LPC_STARTTIME: {
		Label:   "开课时间",
		CName:   "ClassTime",
		Prop:    "startTime",
		Sort:    1,
		Hover:   "",
		Width:   150,
		Fixed:   false,
		Tooltip: false,
		Remark:  "原Lpc",
	},
	HEADER_PREVIEW: {
		Label:  "预习",
		CName:  "Exam",
		Prop:   "preview",
		Sort:   0,
		Hover:  "普通课程：展现学员预习作答正确数、作答数、试卷题目总数信息。ilab课程：展现学员预习成绩，分为优秀、良好、一般三类评级。",
		Width:  100,
		Remark: "原辅导",
	},
	HEADER_LPC_PREVIEW: {
		Label:  "预习（对/答/总）",
		CName:  "LpcExamDetail",
		Prop:   "preView",
		Sort:   0,
		Hover:  "",
		Width:  100,
		Remark: "原Lpc",
	},
	HEADER_ATTEND: {
		Label:  "到课",
		CName:  "Attend",
		Prop:   "attend",
		Sort:   0,
		Hover:  "学员若请假且未到课，则展现学员请假。若学员到课，则展示学员直播课环节中累计到课时长。",
		Width:  100,
		Remark: "原辅导",
	},
	HEADER_LPC_ATTENDSTATUS: {
		Label:  "到课",
		CName:  "ClassType",
		Prop:   "attendStatus",
		Sort:   0,
		Hover:  "",
		Width:  100,
		Remark: "原Lpc",
	},
	HEADER_LPC_FINISHSTATUS: {
		Label:  "完课",
		CName:  "ClassType",
		Prop:   "finishStatus",
		Sort:   0,
		Hover:  "",
		Width:  100,
		Remark: "原Lpc",
	},
	HEADER_PLAYBACK: {
		Label:  "回放观看时长",
		CName:  "Playback",
		Prop:   "playback",
		Sort:   0,
		Hover:  "展示的是用户累计观看回放时长，前14天实时更新，14天之后的数据每日更新一次",
		Width:  100,
		Remark: "原辅导",
	},
	HEADER_PLAYBACK_V1: {
		Label:  "回放观看时长(新)",
		CName:  "Playback",
		Prop:   "playbackV1",
		Sort:   0,
		Hover:  "展示的是回放或LBP的累计观看时长。前60天实时更新，60天之后的数据每日更新一次",
		Width:  100,
		Remark: "原辅导",
	},
	HEADER_LPC_PLAYSTATUS: {
		Label:  "回放",
		CName:  "LpcPlaybackStatus",
		Prop:   "playStatus",
		Sort:   0,
		Hover:  "",
		Width:  100,
		Remark: "原Lpc",
	},
	// 试卷数据header
	HEADER_TESTTYPE: {
		Label: "测试类型",
		CName: "CommonText",
		Prop:  "testType",
		Sort:  0,
		Hover: "",
		Width: 100,
	},
	HEADER_EXAMCOUNT: {
		Label: "题目数量",
		CName: "CommonText",
		Prop:  "examCount",
		Sort:  0,
		Hover: "",
		Width: 100,
	},
	HEADER_PASSRULE: {
		Label: "合格标准",
		CName: "CommonText",
		Prop:  "passRule",
		Sort:  0,
		Hover: "",
		Width: 100,
	},
	HEADER_MAXANSWERTIME: {
		Label: "考试时长",
		CName: "CommonText",
		Prop:  "maxAnswerTime",
		Sort:  0,
		Hover: "",
		Width: 100,
	},
	HEADER_ANSWERTIME: {
		Label: "答题用时",
		CName: "CommonText",
		Prop:  "answerTime",
		Sort:  0,
		Hover: "",
		Width: 100,
	},
	HEADER_RIGHTCOUNT: {
		Label: "正确题数",
		CName: "CommonText",
		Prop:  "rightCount",
		Sort:  0,
		Hover: "",
		Width: 100,
	},
	HEADER_EXAM_SCORE: {
		Label: "得分",
		CName: "LpcExamScore",
		Prop:  "score",
		Sort:  0,
		Hover: "",
		Width: 100,
	},
	HEADER_REPORTURL: {
		Label: "报告[旧]",
		CName: "LpcViewReport",
		Prop:  "reportUrl",
		Sort:  0,
		Hover: "",
		Width: 100,
	},
	// 更多核心数据header
	HEADER_LBPATTENDDURATION: {
		Label:  "录播内容观看时长",
		CName:  "Playback",
		Prop:   "lbpAttendDuration",
		Sort:   0,
		Hover:  "",
		Width:  100,
		Remark: "原辅导",
	},
	HEADER_LBPATTENDDURATIONOLD: {
		Label:  "Lbp观看时长(旧)",
		CName:  "Playback",
		Prop:   "lbpAttendDurationOld",
		Sort:   0,
		Hover:  "",
		Width:  100,
		Remark: "原辅导",
	},
	HEADER_PLAYBACKONLINETIME: {
		Label:  "回放在线时长",
		CName:  "Playback",
		Prop:   "playbackOnlineTime",
		Sort:   0,
		Hover:  "",
		Width:  100,
		Remark: "原辅导",
	},
	HEADER_INCLASSTEST: {
		Label:  "堂堂测",
		CName:  "Exam",
		Prop:   "inclassTest",
		Sort:   0,
		Hover:  "展现学员堂堂测作答正确数、作答数、试卷题目总数信息。",
		Width:  100,
		Remark: "原辅导",
	},
	HEADER_ORALQUESTION: {
		Label:  "口述题",
		CName:  "Exam",
		Prop:   "oralQuestion",
		Sort:   0,
		Hover:  "展现学员口述题提交状态，分为已提交、未提交两种",
		Width:  100,
		Remark: "原辅导",
	},
	HEADER_HOMEWORK: {
		Label:  "巩固练习",
		CName:  "Exam",
		Prop:   "homework",
		Sort:   0,
		Hover:  "普通课程：展现学员巩固练习作答评级，分为S/A/B三类等级。ilab课程：展现学员巩固练习成绩，分为优秀、良好、一般三类评级。",
		Width:  100,
		Remark: "原辅导",
	},
	HEADER_HOMEWORK_LIKE: {
		Label:  "相似题",
		CName:  "Exam",
		Prop:   "similarHomework",
		Sort:   0,
		Hover:  "展现学员错题再练相似题作答评级，分为S/A/B三类等级",
		Width:  100,
		Remark: "原辅导",
	},
	HEADER_EXERCISE: {
		Label:  "直播互动题",
		CName:  "ExerciseAll",
		Prop:   "exercise",
		Sort:   0,
		Hover:  "展现学员直播过程中参与的互动题作答正确数、作答数、试卷题目总数信息。",
		Width:  100,
		Remark: "通用",
	},
	HEADER_EXERCISEALL: {
		Label:  "观看互动题",
		CName:  "ExerciseAll",
		Prop:   "exerciseAll",
		Sort:   0,
		Hover:  " 展现学员直播+回放过程中参与的互动题作答正确数、作答数、试卷题目总数信息。",
		Width:  100,
		Remark: "通用",
	},
	HEADER_LBPINTERACTEXAM: {
		Label:  "录播互动题",
		CName:  "ExerciseAll",
		Prop:   "lbpInteractExam",
		Sort:   0,
		Hover:  "",
		Width:  100,
		Remark: "原辅导",
	},
	HEADER_MIX_PLAYBACK_INTERACT: {
		Label:  "融合直播间回放互动题",
		CName:  "ExerciseAll",
		Prop:   "mixPlaybackInteract",
		Sort:   0,
		Hover:  "",
		Width:  100,
		Remark: "原辅导",
	},
	// 小鹿辅导相关
	HEADER_LITTLE_KID_FUDAO_HOMEWORK_STATUS: {
		Label:   "作业提交状态",
		CName:   "CommonText",
		Prop:    "littleKidFudaoHomeworkStatus",
		Sort:    0,
		Hover:   "",
		Width:   100,
		Fixed:   true,
		Tooltip: false,
		Remark:  "原小鹿辅导",
	},
	HEADER_LITTLE_KID_FUDAO_HOMEWORK_LEVEL: {
		Label:   "作业成绩",
		CName:   "CommonText",
		Prop:    "littleKidFudaoHomeworkLevel",
		Sort:    0,
		Hover:   "",
		Width:   100,
		Fixed:   true,
		Tooltip: false,
		Remark:  "原小鹿辅导",
	},
	HEADER_LITTLE_KID_FUDAO_INTERACT: {
		Label:   "互动题对答总",
		CName:   "CommonText",
		Prop:    "littleKidFudaoInteract",
		Sort:    0,
		Hover:   "",
		Width:   100,
		Fixed:   true,
		Tooltip: false,
		Remark:  "原小鹿辅导",
	},
	HEADER_SYNCHRONOUSPRACTICE: {
		Label:  "同步练习",
		CName:  "CommonText",
		Prop:   "synchronousPractice",
		Sort:   0,
		Hover:  "",
		Width:  100,
		Remark: "原辅导",
	},
	HEADER_HASCOMPOSITIONREPORT: {
		Label:  "作文报告",
		CName:  "CommonText",
		Prop:   "hasCompositionReport",
		Sort:   0,
		Hover:  "",
		Width:  100,
		Remark: "原辅导",
	},

	// 课堂表现header
	HEADER_PRAISE: {
		Label:  "表扬",
		CName:  "CommonText",
		Prop:   "praise",
		Sort:   0,
		Hover:  "",
		Width:  100,
		Remark: "原辅导",
	},
	HEADER_MICROPHONE: {
		Label:  "抢麦",
		CName:  "CommonText",
		Prop:   "microphone",
		Sort:   0,
		Hover:  "",
		Width:  100,
		Remark: "课堂表现",
	},
	HEADER_TALK: {
		Label:  "课中聊天",
		CName:  "CommonText",
		Prop:   "talk",
		Sort:   0,
		Hover:  "",
		Width:  100,
		Remark: "课堂表现",
	},
	HEADER_SCORE: {
		Label:  "学分",
		CName:  "CommonText",
		Prop:   "score",
		Sort:   0,
		Hover:  "",
		Width:  100,
		Remark: "课堂表现",
	},
	HEADER_REWARDNUM: {
		Label:  "星星数",
		CName:  "RewardStar",
		Prop:   "rewardNum",
		Sort:   0,
		Hover:  "展现学员各环节完成后获得的星星数。",
		Width:  100,
		Remark: "原辅导",
	},
	HEADER_MONTHLYEXAMREPORT: {
		Label:  "考试报告",
		CName:  "ExamReport",
		Prop:   "monthlyExamReportUrl",
		Sort:   0,
		Hover:  "需在学员提交堂堂测之后才会生成考试报告哦。",
		Width:  100,
		Remark: "原辅导",
	},
	// LPC相关header
	HEADER_LPC_TEACHERNAME: {
		Label:  "主讲",
		CName:  "CommonText",
		Prop:   "teacherName",
		Sort:   0,
		Hover:  "",
		Width:  100,
		Remark: "原Lpc",
	},
	HEADER_LPC_TANGTANGEXAMSTAT: {
		Label:  "堂堂测（对/答/总）",
		CName:  "LpcExamDetail",
		Prop:   "tangtangExamStat",
		Sort:   0,
		Hover:  "",
		Width:  100,
		Remark: "原Lpc",
	},
	HEADER_LPC_STRENGTHPRACTICE: {
		Label:  "巩固练习",
		CName:  "LpcExamExerciseDetail",
		Prop:   "strengthPractice",
		Sort:   0,
		Hover:  "",
		Width:  100,
		Remark: "原Lpc",
	},
	HEADER_LPC_LESSONREPORTURL: {
		Label:  "课堂报告",
		CName:  "LpcLessonReport",
		Prop:   "lessonReportUrl",
		Sort:   0,
		Hover:  "",
		Width:  100,
		Remark: "原Lpc",
	},
	HEADER_DEER_ELOQUENCE_HOMEWORKLEVEL: {
		Label:  "小鹿口才作业状态",
		CName:  "CommonText",
		Prop:   "deerEloquenceHomeworkLevel",
		Sort:   0,
		Hover:  "",
		Width:  100,
		Remark: "原小鹿",
	},
	HEADER_DEER_PROGRAMMING_HOMEWORK_LEVEL: {
		Label:  "小鹿编程作业状态",
		CName:  "CommonText",
		Prop:   "deerProgrammingHomeworkLevel",
		Sort:   0,
		Hover:  "",
		Width:  100,
		Remark: "原小鹿",
	},
	HEADER_LESSON_REPORT: {
		Label:       "ai课章节报告",
		CName:       "openIframe",
		Prop:        "isSubmitLessonWork",
		Sort:        0,
		Hover:       "",
		Width:       100,
		Remark:      "小鹿使用",
		LinkUrl:     "deerLessonReportUrl",
		FilterMap:   []string{"未生成", "已生成"},
		DialogWidth: 450,
		CopyLinkUrl: 1,
	},
	HEADER_LESSON_HOMEWORK: {
		Label:     "ai课章节作品",
		CName:     "lessonWork",
		Prop:      "isSubmitLessonWork",
		Sort:      0,
		Hover:     "",
		Width:     100,
		Remark:    "小鹿使用",
		FilterMap: []string{"未提交", "已提交"},
	},
	HEADER_ZHIBO_LESSON_REPORT: {
		Label:       "直播课章节报告",
		CName:       "openIframe",
		Prop:        "isGenerateLessonReport",
		Sort:        0,
		Hover:       "",
		Width:       100,
		Remark:      "直播课使用",
		LinkUrl:     "zhiboLessonReport",
		FilterMap:   []string{"未生成", "已生成"},
		DialogWidth: 450,
		CopyLinkUrl: 1,
	},
	HEADER_IS_AI_ATTEND: {
		Label:     "ai课是否到课",
		CName:     "CommonText",
		Prop:      "isDeerAiAttend",
		Sort:      0,
		Hover:     "",
		Width:     100,
		Remark:    "ai课是否到课",
		FilterMap: []string{"未到课", "已到课"},
	},
	HEADER_IS_AI_OVERCLASS: {
		Label:     "ai课是否完课",
		CName:     "CommonText",
		Prop:      "isDeerAiOverClass",
		Sort:      0,
		Hover:     "",
		Width:     100,
		Remark:    "ai课是否完课",
		FilterMap: []string{"未完课", "已完课"},
	},
	HEADER_AI_CONTENTTIME: {
		Label:  "ai课观看时间",
		CName:  "CommonText",
		Prop:   "aiDeerContentTime",
		Sort:   0,
		Hover:  "",
		Width:  100,
		Remark: "ai课观看时长",
	},
	HEADER_DEER_LPC_BC_HOMEWORK: {
		Label:  "小鹿编程作业提交状态",
		CName:  "CommonText",
		Prop:   "deerLpcBcHomeworkStatus",
		Sort:   0,
		Hover:  "",
		Width:  100,
		Remark: "小鹿编程作业提交状态",
	},
	// 更多特殊字段
	HEADER_IS_INCLASS_TEACHER_ROOM_ATTEND_30MINUTE: {
		Label:  "\"到课情况\"（直播章节）",
		CName:  "CommonText",
		Prop:   "isInclassTeacherRoomAttend30minute",
		Sort:   0,
		Hover:  "",
		Width:  100,
		Remark: "辅导",
	},
	HEADER_IS_ATTEND_FINISH: {
		Label:  "\"完课情况\"（直播章节）",
		CName:  "CommonText",
		Prop:   "isAttendFinish",
		Sort:   0,
		Hover:  "",
		Width:  100,
		Remark: "辅导",
	},
	HEADER_GJK_ATTEND_LESSON_LUBO: {
		Label:  "\"到课情况\"（录播章节）",
		CName:  "CommonText",
		Prop:   "gjkAttendLessonLubo",
		Sort:   0,
		Hover:  "",
		Width:  100,
		Remark: "辅导",
	},
	HEADER_GJK_COMPLETE_LESSON_LUBO: {
		Label:  "\"完课情况\"（录播章节）",
		CName:  "CommonText",
		Prop:   "gjkCompleteLessonLubo",
		Sort:   0,
		Hover:  "",
		Width:  100,
		Remark: "辅导",
	},
	HEADER_GJK_LESSON_TAG: {
		Label:  "必看章节",
		CName:  "CommonText",
		Prop:   "gjkLessonTag",
		Sort:   0,
		Hover:  "",
		Width:  100,
		Remark: "章节标签",
	},
}

// GetExamDataHeaders 获取试卷数据专用表头列表
// 对应 PHP 版本的 setExamDataHeader 方法
func GetExamDataHeaders() []string {
	return []string{
		HEADER_TESTTYPE,
		HEADER_EXAMCOUNT,
		HEADER_PASSRULE,
		HEADER_MAXANSWERTIME,
		HEADER_ANSWERTIME,
		HEADER_RIGHTCOUNT,
		HEADER_EXAM_SCORE,
		HEADER_REPORTURL,
	}
}

// MapHeaderFunc 表头处理函数映射
// 迁移自 PHP 版本的 $mapHeaderFunc
// 用于将表头常量映射到对应的处理函数名
var MapHeaderFunc = map[string]string{
	HEADER_TYPE:                                    "GetType",
	HEADER_PLAYTYPE:                                "GetPlayType",
	HEADER_INCLASSTIME:                             "GetInclassTime",
	HEADER_STOPTTIME:                               "GetStopTime",
	HEADER_LESSONNAME:                              "GetLessonName",
	HEADER_STARTTIME:                               "GetStartTime",
	HEADER_PREVIEW:                                 "GetPreview",
	HEADER_ATTEND:                                  "GetAttendData",
	HEADER_PLAYBACK:                                "GetPlayback",
	HEADER_PLAYBACK_V1:                             "GetPlaybackOnlineTimeV1",
	HEADER_LBPATTENDDURATION:                       "GetLbpAttendDuration",
	HEADER_LBPATTENDDURATIONOLD:                    "GetLbpAttendDurationOld",
	HEADER_INCLASSTEST:                             "GetInclassTest",
	HEADER_ORALQUESTION:                            "GetOralQuestion",
	HEADER_HOMEWORK:                                "GetHomeworkData",
	HEADER_HOMEWORK_LIKE:                           "GetHomeworkLikeData",
	HEADER_EXERCISE:                                "GetExerciseColumn",
	HEADER_EXERCISEALL:                             "GetExerciseAllColumn",
	HEADER_LBPINTERACTEXAM:                         "GetLbpInteractExamColumn",
	HEADER_MIX_PLAYBACK_INTERACT:                   "GetMixPlaybackInteract",
	HEADER_LITTLE_KID_FUDAO_HOMEWORK_STATUS:        "GetLittleKidFudaoData",
	HEADER_LITTLE_KID_FUDAO_HOMEWORK_LEVEL:         "GetLittleKidFudaoData",
	HEADER_LITTLE_KID_FUDAO_INTERACT:               "GetLittleKidInteractData",
	HEADER_SYNCHRONOUSPRACTICE:                     "GetSynchronousPractice",
	HEADER_HASCOMPOSITIONREPORT:                    "GetHasCompositionReportData",
	HEADER_TALK:                                    "GetTalk",
	HEADER_SCORE:                                   "GetScoreData",
	HEADER_MONTHLYEXAMREPORT:                       "GetMonthlyExamReportUrl",
	HEADER_IS_INCLASS_TEACHER_ROOM_ATTEND_30MINUTE: "GetIsInclassTeacherRoomAttend30minute",
	HEADER_IS_ATTEND_FINISH:                        "GetIsAttendFinish",
	HEADER_GJK_ATTEND_LESSON_LUBO:                  "GetGjkAttendLessonLubo",
	HEADER_GJK_COMPLETE_LESSON_LUBO:                "GetGjkCompleteLessonLubo",
	HEADER_GJK_LESSON_TAG:                          "GetGjkLessonTag",

	// LPC相关映射
	HEADER_LPC_LESSONNAME:                  "GetLpcLessonName",
	HEADER_LPC_TEACHERNAME:                 "GetLpcTeacherName",
	HEADER_LPC_ATTENDSTATUS:                "GetLpcAttendStatus",
	HEADER_LPC_FINISHSTATUS:                "GetLpcFinishStatus",
	HEADER_LPC_PLAYSTATUS:                  "GetLpcPlayStatus",
	HEADER_LPC_PREVIEW:                     "GetLpcPreViewData",
	HEADER_LPC_TANGTANGEXAMSTAT:            "GetLpcTangTangExamStatData",
	HEADER_LPC_STRENGTHPRACTICE:            "GetLpcStrengthPracticeData",
	HEADER_LPC_LESSONREPORTURL:             "GetLpcLessonReportData",
	HEADER_DEER_ELOQUENCE_HOMEWORKLEVEL:    "GetDeerEloquenceHomeworkLevel",
	HEADER_DEER_PROGRAMMING_HOMEWORK_LEVEL: "GetDeerProgrammingHomeworkLevel",
	HEADER_LESSON_REPORT:                   "GetDeerLessonReport",
	HEADER_LESSON_HOMEWORK:                 "GetLessonHomeWork",
	HEADER_ZHIBO_LESSON_REPORT:             "GetZhiboLessonReport",
}
