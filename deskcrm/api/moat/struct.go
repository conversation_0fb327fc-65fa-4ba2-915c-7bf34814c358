package moat

import (
	"deskcrm/consts"
	"encoding/json"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
)

type BatchGetUserRolesParams struct {
	StudentUids []int64 `json:"studentUids"`
}

type BatchGetUserRolesResp struct {
	UserRoleList map[int64][]int64 `json:"userRoleList"`
}

type RoleConfigItem struct {
	Name  string `json:"name"`
	Value int64  `json:"value"`
}

type GetSkuListByConditionResp struct {
	Total int64 `json:"total"`
	List  []GetSkuListByConditionItem
}

type GetSkuListByConditionItem struct {
	SkuId     int64       `json:"skuId"`
	SkuName   string      `json:"skuName"`
	ThirdId   interface{} `json:"thirdId"`
	Price     int64       `json:"price"`
	Grade     []string    `json:"grade"`
	Subject   []string    `json:"subject"`
	Level     []string    `json:"level"`
	MallSpec  []string    `json:"mall_spec"`
	NewColor  []string    `json:"newColor"`
	BookVer   []string    `json:"bookVer"`
	ForTeam   []string    `json:"forTeam"`
	Status    int64       `json:"status"`    // 上架状态；1=上架，0=下架
	StartTime int64       `json:"startTime"` // 上架时间
	StopTime  int64       `json:"stopTime"`  // 下架时间
	IsInner   int64       `json:"isInner"`   // 内外部商品；1=内部测试，0=外部发布
	// 待有缘人补充
}

type SkuBaseInfoData struct {
	SkuInfoList []SkuInfos `json:"skuInfoList"`
}

type SkuInfos struct {
	AfterSaleTag      int64                  `json:"afterSaleTag"`
	AttributeTags     map[string]interface{} `json:"attributeTags"`
	BusinessDomain    int64                  `json:"businessDomain"`
	BusinessScopeType int64                  `json:"businessScopeType"`
	Category          int                    `json:"category"`
	CombinationType   int64                  `json:"combinationType"`
	CostPrice         int64                  `json:"costPrice"`
	Credit            int64                  `json:"credit"`
	Currency          int64                  `json:"currency"`
	FreightPriceTag   int64                  `json:"freightPriceTag"`
	IsDelete          int64                  `json:"isDelete"`
	IsInner           int64                  `json:"isInner"`
	ItemId            int64                  `json:"itemId"`
	KidChineseCoin    int64                  `json:"kidChineseCoin"`
	KidMagicStones    int64                  `json:"kidMagicStones"`
	KidPracticeCoin   int64                  `json:"kidPracticeCoin"`
	LabelTags         map[string]interface{} `json:"labelTags"`
	OrderLimit        int64                  `json:"orderLimit"`
	Price             int64                  `json:"price"`
	ShopId            int64                  `json:"shopId"`
	ShortSkuName      string                 `json:"shortSkuName"`
	SkuId             int64                  `json:"skuId"`
	SkuMode           int                    `json:"skuMode"`
	SkuName           string                 `json:"skuName"`
	SkuOriginPrice    int64                  `json:"skuOriginPrice"`
	Source            int                    `json:"source"`
	SpecTags          map[string]interface{} `json:"specTags"`
	SpuId             int64                  `json:"spuId"`
	StarCurrency      int64                  `json:"starCurrency"`
	TaxationCode      string                 `json:"taxationCode"`
	ThirdId           int64                  `json:"thirdId"`
}

type FormatSkuInfos struct {
	CourseTimeFormat    string   `json:"courseTimeFormat"`
	LessonDisplayTxt    string   `json:"lessonDisplayTxt"`
	LessonNum           string   `json:"lessonNum"`
	TeacherNameList     []string `json:"teacherNameList"`
	LearnSeason         string   `json:"learnSeason"`
	Subject             string   `json:"subject"`
	OnlineFormatTimeAll string   `json:"onlineFormatTimeAll"`
	OnlineFormatTime    string   `json:"onlineFormatTime"`
}

type GetOneOpenSearchResp struct {
	List []OneOpenSearchItem `json:"list"`
}

type OneOpenSearchItem struct {
	UserId  int64 `json:"userId"`
	OrderId int64 `json:"orderId"`
}

// OneOpenListItem 用户订单列表项
type OneOpenListItem struct {
	OrderId             int64               `json:"orderId"`             // 订单ID
	OrderTime           int64               `json:"orderTime"`           // 下单时间戳
	PayTime             int64               `json:"payTime"`             // 支付时间戳
	BusinessType        int                 `json:"businessType"`        // 业务类型
	PayableAmount       int64               `json:"payableAmount"`       // 应付金额（分）
	PaidAmount          int64               `json:"paidAmount"`          // 实付金额（分）
	OrderStatus         int                 `json:"orderStatus"`         // 订单状态
	OrderBusinessStatus int                 `json:"orderBusinessStatus"` // 订单业务状态
	CurrencyType        int                 `json:"currencyType"`        // 货币类型
	SkuRowList          []OneOpenListSkuRow `json:"skuRowList"`          // SKU行列表
}

// OneOpenListSkuRow SKU行信息
type OneOpenListSkuRow struct {
	SkuId       int64  `json:"skuId"`       // SKU ID
	SkuName     string `json:"skuName"`     // SKU名称
	SkuRowId    int64  `json:"skuRowId"`    // SKU行ID
	ShopId      int    `json:"shopId"`      // 店铺ID
	ProductId   int64  `json:"productId"`   // 产品ID
	GoodsAmount int64  `json:"goodsAmount"` // 商品金额（分）
	PaidAmount  int64  `json:"paidAmount"`  // 实付金额（分）
}

// AfterPlatSearchItem 售后订单搜索项
// 对应 PHP 的 /afterplat/after/search 接口返回的售后订单项
type AfterPlatSearchItem struct {
	OrderId    int64 `json:"orderId"`    // 订单ID
	CreateTime int64 `json:"createTime"` // 创建时间
	Status     int   `json:"status"`     // 售后状态
}

// AfterPlatSearchData 售后订单搜索数据
type AfterPlatSearchData struct {
	List []AfterPlatSearchItem `json:"list"`
}

type ThirdOrderSearchItem struct {
	TreadNo               int64 `json:"treadNo"`
	DeliveryAddressStatus int64 `json:"deliveryAddressStatus"`
	// 不固定字段使用 map 接收
	Extra map[string]interface{} `json:"-"`
}

// 自定义 UnmarshalJSON 方法
func (t *ThirdOrderSearchItem) UnmarshalJSON(data []byte) error {
	// 定义一个临时的结构体，避免无限递归
	type Alias ThirdOrderSearchItem
	aux := struct {
		*Alias
	}{
		Alias: (*Alias)(t),
	}

	// 解析固定字段
	if err := jsoniter.Unmarshal(data, &aux); err != nil {
		return err
	}

	// 初始化 Extra
	t.Extra = make(map[string]interface{})

	// 将所有字段解析到一个 map 中
	var rawMap map[string]interface{}
	if err := jsoniter.Unmarshal(data, &rawMap); err != nil {
		return err
	}

	// 将未知字段放入 Extra
	for key, value := range rawMap {
		switch key {
		case "treadNo", "deliveryAddressStatus":
			// 跳过已定义的字段
			continue
		default:
			t.Extra[key] = value
		}
	}

	return nil
}

type AfterDetail struct {
	Id               int64           `json:"id"`               // 售后记录ID
	RequestNo        string          `json:"requestNo"`        // 请求号
	ShopId           int             `json:"shopId"`           // 店铺ID
	AfterId          int64           `json:"afterId"`          // 售后单ID
	UserId           int64           `json:"userId"`           // 用户ID
	OrderId          int64           `json:"orderId"`          // 订单ID
	ReasonId         int             `json:"reasonId"`         // 原因ID
	SkuMode          int             `json:"skuMode"`          // SKU模式
	SkuServiceType   int             `json:"skuServiceType"`   // SKU服务类型
	Flag             int             `json:"flag"`             // 标志
	CurrencyType     int             `json:"currencyType"`     // 货币类型
	ApplyUid         int64           `json:"applyUid"`         // 申请用户ID
	ApplyFrom        int             `json:"applyFrom"`        // 申请来源
	ApplyPlat        string          `json:"applyPlat"`        // 申请平台
	ApplyAfterType   int             `json:"applyAfterType"`   // 申请售后类型
	CheckAfterType   int             `json:"checkAfterType"`   // 审核售后类型
	ProcessAfterType int             `json:"processAfterType"` // 处理售后类型
	Step             int             `json:"step"`             // 步骤
	Status           int             `json:"status"`           // 状态
	CheckPlan        int             `json:"checkPlan"`        // 审核计划
	CancelType       int             `json:"cancelType"`       // 取消类型
	SortValue        int             `json:"sortValue"`        // 排序值
	CheckTime        int64           `json:"checkTime"`        // 审核时间
	UpdateTime       int64           `json:"updateTime"`       // 更新时间
	CreateTime       int64           `json:"createTime"`       // 创建时间
	ProducerSys      int             `json:"producerSys"`      // 生产系统
	FinishTime       int64           `json:"finishTime"`       // 完成时间
	RefundInfo       AfterRefundInfo `json:"refundInfo"`       // 退款信息
	OrderVersion     int             `json:"orderVersion"`     // 订单版本
	SaleChannel      int             `json:"saleChannel"`      // 销售渠道
	ApplyUName       string          `json:"applyUName"`       // 申请用户名
	Remark           string          `json:"remark"`           // 备注
	StepName         string          `json:"stepName"`         // 步骤名称
	StepText         string          `json:"stepText"`         // 步骤文本
	Reason           string          `json:"reason"`           // 原因
}

// AfterRefundInfo 退款信息
type AfterRefundInfo struct {
	RefundId            int64               `json:"refundId"`            // 退款ID
	BatchId             int64               `json:"batchId"`             // 批次ID
	CurrencyType        int                 `json:"currencyType"`        // 货币类型
	RefundGoodsAmount   int64               `json:"refundGoodsAmount"`   // 退款商品金额
	RefundExpressAmount int64               `json:"refundExpressAmount"` // 退款运费金额
	RefundDeductAmount  int64               `json:"refundDeductAmount"`  // 退款扣减金额
	Status              int                 `json:"status"`              // 退款状态
	CreateTime          int64               `json:"createTime"`          // 退款创建时间
	UpdateTime          int64               `json:"updateTime"`          // 退款更新时间
	SkuRowList          []AfterSkuRowRefund `json:"skuRowList"`          // SKU行退款列表
}

// AfterSkuRowRefund SKU行退款信息
type AfterSkuRowRefund struct {
	SkuRowId          int64 `json:"skuRowId"`          // SKU行ID
	Quantity          int   `json:"quantity"`          // 数量
	ShareGoodsAmount  int64 `json:"shareGoodsAmount"`  // 分摊商品金额
	RefundGoodsAmount int64 `json:"refundGoodsAmount"` // 退款商品金额
}

// SkuInfo SKU信息（简化版，用于基本信息）
type SkuInfo struct {
	SkuId         int64  `json:"skuId"`
	SkuName       string `json:"skuName"`
	Price         int64  `json:"price"`         // 当前价格（分）
	OriPrice      int64  `json:"oriPrice"`      // 原价（分）
	DiscountPrice int64  `json:"discountPrice"` // 优惠金额（分）
}

// SkuDetailInfo SKU详细信息（完整版，用于KV查询）
type SkuDetailInfo struct {
	SkuId                int64                    `json:"skuId"`
	SkuName              string                   `json:"skuName"`
	SpuId                int64                    `json:"spuId"`
	SpuName              string                   `json:"spuName"`
	SkuMode              int                      `json:"skuMode"`
	CategoryId           int                      `json:"categoryId"`
	CategoryPid          int                      `json:"categoryPid"`
	ThirdId              int64                    `json:"thirdId"`
	IsInner              int                      `json:"isInner"`
	IsDelete             int                      `json:"isDelete"`
	Version              int                      `json:"version"`
	ProductContent       []map[string]interface{} `json:"productContent"`
	SkuServiceType       int                      `json:"skuServiceType"`
	PerformType          int                      `json:"performType"`
	ProducerSys          int                      `json:"producerSys"`
	ThirdCode            string                   `json:"thirdCode"`
	ShortSkuName         string                   `json:"shortSkuName"`
	Status               int                      `json:"status"`
	StartTime            int64                    `json:"startTime"`
	StopTime             int64                    `json:"stopTime"`
	FirstSaleTime        int64                    `json:"firstSaleTime"`
	PointConvertCurrency map[string]interface{}   `json:"pointConvertCurrency"`
	Currency             []map[string]interface{} `json:"currency"`
	SkuUnitDetail        []map[string]interface{} `json:"skuUnitDetail"`
	BusinessScopeType    int                      `json:"businessScopeType"`
	CalPriceType         int                      `json:"calPriceType"`
	StoreType            int                      `json:"storeType"`
	ShopId               int                      `json:"shopId"`
	ShopPayMix           int                      `json:"shopPayMix"`
	IsBindViceSku        int                      `json:"isBindViceSku"`
	CombinationType      int                      `json:"combinationType"`
	GoodsType            int                      `json:"goodsType"`
	ViceSkuList          []map[string]interface{} `json:"viceSkuList"`
	ChildSkuList         []map[string]interface{} `json:"childSkuList"`
	AttributeTags        []map[string]interface{} `json:"attributeTags"`
	SpecTags             []map[string]interface{} `json:"specTags"`
	Specs                []map[string]interface{} `json:"specs"`
	LabelTags            []map[string]interface{} `json:"labelTags"`
	Resource             []map[string]interface{} `json:"resource"`
	MediaResource        map[string]interface{}   `json:"mediaResource"`
	OrderLimit           int                      `json:"orderLimit"`
	FreightPriceTag      int                      `json:"freightPriceTag"`
	BusinessDomain       int                      `json:"businessDomain"`
	TaxationCode         string                   `json:"taxationCode"`
	InnerSort            int                      `json:"innerSort"`
	GoodsMarks           []map[string]interface{} `json:"goodsMarks"`
	ShopClassifyIds      []map[string]interface{} `json:"shopClassifyIds"`
	GroupIds             []map[string]interface{} `json:"groupIds"`
	AfterSaleTag         int                      `json:"afterSaleTag"`
	ExtData              map[string]interface{}   `json:"extData"`
}

// SkuResponse SKU查询响应
type SkuResponse struct {
	ErrNo int                `json:"errNo"`
	Data  map[string]SkuInfo `json:"data"`
}

// SkuKVResponse SKU KV查询响应（返回数组格式）
type SkuKVResponse struct {
	ErrNo int             `json:"errNo"`
	Data  []SkuDetailInfo `json:"data"`
}

// ExpressInfo 物流信息
type ExpressInfo struct {
	OrderDetails    []OrderDetail `json:"orderDetails"`    // 订单详情列表
	ReceiverAddress string        `json:"receiverAddress"` // 收货地址
	ReceiverName    string        `json:"receiverName"`    // 收货人姓名
	ReceiverPhone   string        `json:"receiverPhone"`   // 收货人电话
	StatusName      string        `json:"statusName"`      // 状态名称
	StatusFlows     []interface{} `json:"statusFlows"`     // 状态流程
	SendType        string        `json:"sendType"`        // 发送类型
	SendTime        int64         `json:"sendTime"`        // 发送时间
	ExpressNumber   string        `json:"expressNumber"`   // 快递单号
	Status          int           `json:"status"`          // 状态
	PackTime        int64         `json:"packTime"`        // 打包时间
}

// OrderDetail 订单详情
type OrderDetail struct {
	SkuRowId int64  `json:"skuRowId"` // SKU行ID
	OrderId  int64  `json:"orderId"`  // 订单ID
	Uid      int64  `json:"uid"`      // 用户ID
	SkuName  string `json:"skuName"`  // SKU名称
	Quantity int    `json:"quantity"` // 数量
}

// TradeListItem 订单列表项
type TradeListItem struct {
	OrderId int64 `json:"orderId"`
	UserId  int64 `json:"userId"`
}

// TradeListResponse 订单列表响应
type TradeListResponse struct {
	Total int             `json:"total"`
	List  []TradeListItem `json:"list"`
}

// SubTradeInfo 子订单信息
type SubTradeInfo struct {
	SubTradeId  string      `json:"subTradeId"`
	TradeId     string      `json:"tradeId"`
	SkuId       int64       `json:"skuId"`
	CourseId    int64       `json:"courseId"`
	ItemTag     string      `json:"itemTag"`
	TradeFee    int64       `json:"tradeFee"`
	TradeTime   int64       `json:"tradeTime"`
	Status      int         `json:"status"`
	ProductType int         `json:"productType"`
	RefundInfo  *RefundInfo `json:"refundInfo"`
}

// RefundInfo 退款信息
type RefundInfo struct {
	RefundStartTime int64 `json:"refundStartTime"`
}

// SkuRowItem SKU行项目信息
type SkuRowItem struct {
	SkuRowId            int64         `json:"skuRowId"`
	Quantity            int           `json:"quantity"`
	SkuId               int64         `json:"skuId"`
	MainSkuId           int64         `json:"mainSkuId"`
	MainSkuVersionId    int64         `json:"mainSkuVersionId"`
	SkuVersionId        int64         `json:"skuVersionId"`
	SkuMode             int           `json:"skuMode"`
	ProducerSys         int           `json:"producerSys"`
	ProductId           int64         `json:"productId"`
	IsGift              int           `json:"isGift"`
	SkuType             int           `json:"skuType"`
	IsMain              int           `json:"isMain"`
	PaidAmount          int64         `json:"paidAmount"`
	PaidExpressAmount   int64         `json:"paidExpressAmount"`
	GoodsAmount         int64         `json:"goodsAmount"`
	PackageTime         int64         `json:"packageTime"`
	PackageStatus       int           `json:"packageStatus"`
	SkuServiceType      int           `json:"skuServiceType"`
	SkuFlag             int           `json:"skuFlag"`
	Points              int64         `json:"points"`
	ShopId              int           `json:"shopId"`
	ExpressAmount       int64         `json:"expressAmount"`
	AfterBizInfo        interface{}   `json:"afterBizInfo"`
	ProductCode         string        `json:"productCode"`
	SkuName             string        `json:"skuName"`
	OrderBusinessStatus int           `json:"orderBusinessStatus"`
	RefundStatus        int           `json:"refundStatus"`
	AfterType           interface{}   `json:"afterType"` // 售后类型映射 (afterId => afterType)
	SkuRowFlag          []interface{} `json:"skuRowFlag"`
}

// AfterBizInfo 售后业务信息
type AfterBizInfo struct {
	IsChange       int    `json:"isChange"`
	IsRefund       int    `json:"isRefund"`
	NoChangeReason string `json:"noChangeReason"`
	NoRefundReason string `json:"noRefundReason"`
}

// AddressInfo 地址信息
type AddressInfo struct {
	Province     string `json:"province"`
	City         string `json:"city"`
	District     string `json:"district"`
	DetailAddr   string `json:"detailAddr"`
	ReceiverName string `json:"receiverName"`
	ReceiverTel  string `json:"receiverTel"`
}

// DiscountInfo 优惠信息
type DiscountInfo struct {
	DiscountAmount int64  `json:"discountAmount"`
	DiscountType   int    `json:"discountType"`
	DiscountDesc   string `json:"discountDesc"`
}

// AssembleDetail 组合品详情
type AssembleDetail struct {
	AssembleOrderId string           `json:"assembleOrderId"`
	AssembleType    int              `json:"assembleType"`
	AssembleStatus  int              `json:"assembleStatus"`
	SkuRowList      []AssembleSkuRow `json:"skuRowList"`
}

// AssembleSkuRow 组合品SKU行项目
type AssembleSkuRow struct {
	OrderId int64 `json:"orderId"`
	// 其他字段可以根据实际需要添加
}

// TradeDetailItem 订单详情项
type TradeDetailItemOri struct {
	OrderId             int64            `json:"orderId"`
	UserId              int64            `json:"userId"`
	OrderTime           int64            `json:"orderTime"`
	PayTime             int64            `json:"payTime"`
	BusinessType        int              `json:"businessType"`
	PayableAmount       int64            `json:"payableAmount"`
	PaidAmount          int64            `json:"paidAmount"`
	OrderStatus         int              `json:"orderStatus"`
	OrderBusinessStatus int              `json:"orderBusinessStatus"`
	LogInfo             interface{}      `json:"logInfo"`
	SkuRowList          []SkuRowItem     `json:"skuRowList"`
	BindDetail          []interface{}    `json:"bindDetail"`
	AddressInfo         interface{}      `json:"addressInfo"`
	DiscountInfo        interface{}      `json:"discountInfo"`
	CurrencyType        int              `json:"currencyType"`
	AssembleDetail      []AssembleDetail `json:"assembleDetail"`
	SubList             []SubTradeInfo   `json:"subList"`
	OriginalOrderId     int64            `json:"originalOrderId"`
	TransDetail         []interface{}    `json:"transDetail"`
}

func (t *TradeDetailItemOri) Convert(ctx *gin.Context) TradeDetailItem {
	item := TradeDetailItem{
		OrderId:             t.OrderId,
		UserId:              t.UserId,
		OrderTime:           t.OrderTime,
		PayTime:             t.PayTime,
		BusinessType:        t.BusinessType,
		PayableAmount:       t.PayableAmount,
		PaidAmount:          t.PaidAmount,
		OrderStatus:         t.OrderStatus,
		OrderBusinessStatus: t.OrderBusinessStatus,
		LogInfo:             t.LogInfo,
		SkuRowList:          t.SkuRowList,
		BindDetail:          t.BindDetail,
		DiscountInfo:        t.DiscountInfo,
		CurrencyType:        t.CurrencyType,
		AssembleDetail:      t.AssembleDetail,
		SubList:             t.SubList,
		OriginalOrderId:     t.OriginalOrderId,
		TransDetail:         t.TransDetail,
	}

	// 处理AddressInfo，将interface{}转换为DetailAddressInfo
	if t.AddressInfo != nil {
		data, _ := json.Marshal(t.AddressInfo)
		if string(data) == "[]" {
			return item
		}
		err := json.Unmarshal(data, &item.AddressInfo)
		if err != nil {
			zlog.Errorf(ctx, "json.Unmarshal failed, err: %v,%v", err, string(data))
		}
		err = json.Unmarshal(data, &item.AddressInfo)
		if err != nil {
			zlog.Errorf(ctx, "json.Unmarshal failed, err: %v,%v", err, string(data))
		}
	}

	return item
}

type TradeDetailItem struct {
	OrderId             int64             `json:"orderId"`
	UserId              int64             `json:"userId"`
	OrderTime           int64             `json:"orderTime"`
	PayTime             int64             `json:"payTime"`
	BusinessType        int               `json:"businessType"`
	PayableAmount       int64             `json:"payableAmount"`
	PaidAmount          int64             `json:"paidAmount"`
	OrderStatus         int               `json:"orderStatus"`
	OrderBusinessStatus int               `json:"orderBusinessStatus"`
	LogInfo             interface{}       `json:"logInfo"`
	SkuRowList          []SkuRowItem      `json:"skuRowList"`
	BindDetail          []interface{}     `json:"bindDetail"`
	AddressInfo         DetailAddressInfo `json:"addressInfo"`
	DiscountInfo        interface{}       `json:"discountInfo"`
	CurrencyType        int               `json:"currencyType"`
	AssembleDetail      []AssembleDetail  `json:"assembleDetail"`
	SubList             []SubTradeInfo    `json:"subList"`
	OriginalOrderId     int64             `json:"originalOrderId"`
	TransDetail         []interface{}     `json:"transDetail"`
}

type DetailAddressInfo struct {
	AddressId    int    `json:"addressId"`
	ProvinceId   int    `json:"provinceId"`
	CityId       int    `json:"cityId"`
	TownId       int    `json:"townId"`
	PrefectureId int    `json:"prefectureId"`
	Name         string `json:"name"`
	Phone        string `json:"phone"`
	Province     string `json:"province"`
	City         string `json:"city"`
	Prefecture   string `json:"prefecture"`
	Town         string `json:"town"`
	Address      string `json:"address"`
}

// DarKVByCourseIdsResp 根据课程ID查询订单KV信息响应
type DarKVByCourseIdsResp struct {
	Data map[string]DarKVByCourseIdsItem `json:"data"`
}

// DarKVByCourseIdsItem 课程订单KV信息项
type DarKVByCourseIdsItem struct {
	UserId              int64       `json:"userId"`              // 用户ID
	CourseId            int64       `json:"courseId"`            // 课程ID
	TradeId             int64       `json:"tradeId"`             // 主订单ID
	SubTradeId          int64       `json:"subTradeId"`          // 子订单ID
	TradeFee            int64       `json:"tradeFee"`            // 交易金额（分）
	TradeTime           int64       `json:"tradeTime"`           // 交易时间戳
	OrderBusinessStatus int         `json:"orderBusinessStatus"` // 订单业务状态
	RefundStatus        int         `json:"refundStatus"`        // 退款状态
	RefundStartTime     int64       `json:"refundStartTime"`     // 退款开始时间
	ChangeTime          int64       `json:"changeTime"`          // 变更时间
	ChangeFromCourseId  int64       `json:"changeFromCourseId"`  // 变更前课程ID
	ChangeToCourseId    int64       `json:"changeToCourseId"`    // 变更后课程ID
	CreateTime          int64       `json:"createTime"`          // 创建时间
	UpdateTime          int64       `json:"updateTime"`          // 更新时间
	LogInfo             interface{} `json:"logInfo"`             // 日志信息
	ExpressStatus       int         `json:"expressStatus"`       // 快递状态
	RefundInfo          interface{} `json:"refundInfo"`          // 退款信息
	SpecialSkuType      int         `json:"specialSkuType"`      // 特殊SKU类型
	ItemTag             string      `json:"itemTag"`             // 商品标签
	CourseInfo          interface{} `json:"courseInfo"`          // 课程信息
	SkuId               int64       `json:"skuId"`               // SKU ID
	Status              int64       `json:"status"`              // 计算后的订单状态，用于 courseRecordV2
}

// GetStatus 根据订单业务状态和退款状态计算交易状态
// 对应PHP的 Fudao_Dar::getTradeStatus 方法
func (item *DarKVByCourseIdsItem) GetStatus() int {
	return consts.GetTradeStatus(item.OrderBusinessStatus, item.RefundStatus)
}

// ShortUrlData 短链接数据
type ShortUrlData struct {
	ShortUrl  string `json:"shortUrl"`  // 短链接
	ShortCode string `json:"shortCode"` // 短链接代码
}
