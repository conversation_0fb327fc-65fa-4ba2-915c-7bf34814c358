package jxreport

import (
	"deskcrm/api"
	"deskcrm/conf"
	"deskcrm/libs/utils"
	"encoding/json"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

type Client struct {
	cli *base.ApiClient
}

// NewClient create Client instance
func NewClient() *Client {
	c := &Client{
		cli: conf.API.JxReport,
	}
	return c
}

const (
	GetLessonReportUrlLpcAPI = "/jxreport/api/getlessonreporturllpc"
	GetLessonReportUrlAPI    = "/jxreport/api/getlessonreporturllist"
)

// 单次最大1000个学生
func (c *Client) GetLessonReportUrlLpc(ctx *gin.Context, lessonID int64, studentUIDs []int64) (reportMap map[int64]*ReportUrlDetail, err error) {
	reportMap = map[int64]*ReportUrlDetail{}
	//lessonID = 528775
	//studentUIDs = []int64{2135410777}
	req := map[string]interface{}{
		"lessonId": lessonID,
		"uidList":  studentUIDs,
	}
	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpPost(ctx, GetLessonReportUrlLpcAPI, opts)
	if err != nil {
		zlog.Warnf(ctx, "GetLessonReportUrlLpc err:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	resp := make(map[string]map[string]json.RawMessage)
	if len(cast.ToString(res.Response)) == 0 {
		return
	}

	if _, err = api.DecodeResponse(ctx, res, &resp); err != nil {
		return
	}
	if _, ok := resp[cast.ToString(lessonID)]; !ok {
		return
	}
	if len(resp[cast.ToString(lessonID)]) == 0 {
		return
	}

	for studentUID, reportDetailJsonStr := range resp[cast.ToString(lessonID)] {
		if string(reportDetailJsonStr) == "[]" { //接口中有数据返回的是对象，没数据的时候会返回一个空的数组，这里做兼容
			continue
		}
		uid := cast.ToInt64(studentUID)
		reportDetail := &ReportUrlDetail{}
		err = json.Unmarshal(reportDetailJsonStr, reportDetail)
		if err != nil {
			zlog.Warnf(ctx, "json.Unmarshal err:%v", err)
			continue
		}
		reportMap[uid] = reportDetail
	}
	return reportMap, nil
}

func (c *Client) GetLessonReportUrl(ctx *gin.Context, lessonIds []int64, studentUid int64) (reportMap map[int64]*ReportUrlDetail, err error) {
	reportMap = map[int64]*ReportUrlDetail{}
	req := map[string]interface{}{
		"lessonIdArr": lessonIds,
		"studentUid":  studentUid,
	}
	opts := base.HttpRequestOptions{RequestBody: req, Encode: base.EncodeForm}
	utils.DecorateHttpOptions(ctx, &opts)

	res, err := c.cli.HttpPost(ctx, GetLessonReportUrlAPI, opts)
	if err != nil {
		zlog.Warnf(ctx, "GetLessonReportUrl err:%v", err)
		return
	}

	if err = api.ApiHttpCode(ctx, res); err != nil {
		return
	}

	resp := make(map[int64]*ReportUrlDetail)
	if _, err = api.DecodePhpResponse(ctx, res, &resp); err != nil {
		return
	}

	return resp, nil
}
