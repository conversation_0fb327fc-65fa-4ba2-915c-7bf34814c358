<?php

class Action_GetCourseTimeTableDay extends AssistantDesk_ActionCommon {

    protected $needCheckSelectedAssistant = true;

    public function invoke() {

        $this->checkLogin();

        $this->_requestParam['personUid']    = isset($this->_userInfo['personUid']) ? intval($this->_userInfo['personUid']) : 0;
        $this->_requestParam['assistantUid'] = isset($this->_userInfo['assistantUid']) ? intval($this->_userInfo['assistantUid']) : 0;

        $objPS     = new Service_Page_DeskV1_Student_GetCourseTimeTableDay();
        $arrOutput = $objPS->execute($this->_requestParam);


        return $arrOutput;
    }
}


